# ADK 知深导师 AI 持久化功能实施计划书 (V2)

## 1. 概述

本计划旨在为“ADK 知深导师 AI”项目增加数据持久化能力，解决当前对话历史无法保存、无法处理外部文档作为知识库的问题。我们将通过引入文件系统存储，实现会话的恢复、知识的沉淀和管理。**V2 版本将结合 `adk-python` 官方框架的最佳实践，提供更详细的技术实现细节。**

**核心目标:**
1.  实现一个遵循 `adk-python` 规范的、基于文件系统的会话服务 (`FileSessionService`)。
2.  设计并实现一个清晰、可扩展的数据存储目录结构。
3.  增加处理外部文档（Markdown, TXT）并将其作为知识库的功能。

---

## 2. 持久化目录结构设计

我们将创建一个主数据目录 `session_data` 来存放所有持久化信息。每个独立的对话会话将拥有自己的子目录，以 `session_id` 命名。

```
/session_data/
└───{session_id_1}/              # 单次会话的根目录
    ├───knowledge/               # 存放本次会话中处理过的所有知识文档
    │   ├───doc_1.md
    │   └───doc_2.txt
    ├───history.jsonl            # 完整的对话历史（JSON Lines 格式，便于追加）
    ├───compressed_history.json  # 压缩后的对话历史
    └───state.json               # 会话的其他状态信息（例如 Token 统计）
└───{session_id_2}/
    ├───...
```

**设计理由:**
*   **隔离性**: 每个会话的数据都封装在自己的目录中，清晰明了，便于管理和迁移。
*   **可扩展性**: 未来如果增加更多类型的数据（如用户画像、反馈等），只需在会话目录下增加新的文件或子目录即可。
*   **格式选择**:
    *   `history.jsonl`: 采用 JSON Lines 格式，每一行是一个独立的 JSON 对象（代表一轮对话），非常适合流式读写和追加，避免了反复读写整个大型 JSON 文件带来的性能问题。
    *   `.json`: 对于压缩历史和会话状态，它们通常是整体读写的，使用标准 JSON 格式更直观。

---

## 3. 文件会话服务 (FileSessionService) 实现

我们将严格遵循 `adk-python` 的 `BaseSessionService` 接口，创建一个新的 `FileSessionService`。

**步骤:**

1.  **创建新文件**: 在 `adk_ai_project/` 目录下创建一个新的子目录 `services`，并在其中创建文件 `file_session_service.py`。

2.  **实现 `FileSessionService` 类**: 该类将继承 `google.adk.sessions.BaseSessionService` 并实现其所有抽象方法。

    ```python
    # adk_ai_project/services/file_session_service.py
    import os
    import json
    import aiofiles
    from google.adk.sessions import BaseSessionService, Session
    from google.genai import types as genai_types

    class FileSessionService(BaseSessionService):
        """A session service that stores session data in the local filesystem."""

        def __init__(self, base_path: str = "session_data"):
            self._base_path = base_path
            os.makedirs(self._base_path, exist_ok=True)

        async def create_session(self, app_name: str, user_id: str, ...) -> Session:
            # ... 实现创建会话目录和初始文件的逻辑 ...
            pass

        async def get_session(self, app_name: str, user_id: str, session_id: str, ...) -> Session:
            # ... 实现从文件加载历史和状态，重建 Session 对象的逻辑 ...
            pass

        async def update_session(self, session: Session):
            # ... 实现将 Session 对象的状态写回文件的逻辑 ...
            pass

        async def list_sessions(self, app_name: str, user_id: str) -> list[Session]:
            # ... 实现列出用户所有会话的逻辑 ...
            pass

        async def delete_session(self, app_name: str, user_id: str, session_id: str):
            # ... 实现删除会话目录的逻辑 ...
            pass
            
        async def append_event(self, session: Session, event: genai_types.Event):
            # ... 实现将单个 Event 追加到 history.jsonl 的逻辑 ...
            pass
    ```

3.  **替换服务**:
    *   在 `adk_ai_project/main.py` 中，进行如下修改：
        ```python
        # from google.adk.sessions import InMemorySessionService
        from .services.file_session_service import FileSessionService # 替换导入

        # ...

        # session_service = InMemorySessionService()
        session_service = FileSessionService(base_path="session_data") # 替换实例
        ```

---

## 4. 文档解析与知识持久化

我们将扩展现有逻辑，使其能够接收文件路径作为输入，并将其内容作为知识进行处理和存储。这个功能将主要在 `main.py` 的交互循环中实现，以便未来可以轻松适配 `adk-web` 的文件上传逻辑。

**步骤:**

1.  **修改用户输入处理逻辑**:
    *   在 `adk_ai_project/main.py` 的 `while True:` 循环中，增加一个逻辑判断：
        ```python
        import os

        user_input = await asyncio.to_thread(input, "🧑‍💻 User: ")
        
        # 新增逻辑：检查输入是否为有效的文件路径
        if os.path.isfile(user_input) and user_input.endswith(('.md', '.txt')):
            print(f"正在解析文档: {user_input}")
            async with aiofiles.open(user_input, mode='r', encoding='utf-8') as f:
                file_content = await f.read()
            
            # 将文件内容作为知识注入，而不是普通对话
            # 这里我们将创建一个特殊的 Event 或直接调用知识注入的回调
            # 为了简化，我们先将其作为普通消息，让意图分析模块处理
            user_input = file_content 
            # TODO: 后续可以优化为专门的知识注入事件
        
        # ... 后续代码不变 ...
        ```

2.  **知识的保存与应用**:
    *   **保存**: 当 `DialogueUtilityAgent.classify_intent` 将内容判断为 `knowledge_injection` 时，`after_main_model_callback` 会将其存入 `pending_knowledge` 状态。我们需要修改这个回调，让它同时将这份知识保存到 `session_data/{session_id}/knowledge/` 目录中。
    *   **应用**: `before_main_model_callback` 的现有逻辑会自动检测到 `pending_knowledge` 并注入到下一次的 LLM 请求中。这个机制保持不变，但现在知识的来源有了文件系统的备份。

## 5. 实施路线图

1.  **Phase 1: 结构与服务**
    *   **任务**: 完成 `FileSessionService` 类的完整实现，包括所有 `BaseSessionService` 的抽象方法。
    *   **验证**: 能够在 `adk_ai_project/main.py` 中成功替换 `InMemorySessionService`，程序可以正常启动，并在 `session_data` 目录下创建空的会话结构。

2.  **Phase 2: 核心功能联调**
    *   **任务**: 实现 `get_session` 和 `update_session` 的核心读写逻辑。实现 `main.py` 中的文件路径识别功能。
    *   **验证**: 进行一次多轮对话，退出程序后，重新启动并传入相同的 `session_id`，能够看到对话历史被成功加载。

3.  **Phase 3: 知识处理与完善**
    *   **任务**: 修改回调函数，将识别出的知识点保存到 `knowledge` 目录。完善所有服务的错误处理和边缘情况（如文件损坏、权限问题等）。
    *   **验证**: 能够通过在命令行输入文件路径来注入知识，并在后续对话中看到 Agent 使用了该知识。所有功能稳定运行。