# 系统架构（Architecture）

本项目基于 google/adk-python，围绕“知深导师 AI”的多轮对话与 Token 优化展开。本文档描述关键组件、数据流与扩展点。

## 组件概览
- ZhishenMentorAgent：核心导师 Agent，负责对话生成
- DialogueUtilityAgent：工具 Agent，负责意图分析与历史压缩
- ZhishenMentorPipeline：顶层 Pipeline，编排对话、产出状态快照
- SnapshotWrapperAgent：包装器，在回复后输出状态快照事件
- FileSessionService：会话持久化服务（history/knowledge/统计）
- 回调：before_main_model_callback / after_main_model_callback

## 调用流程（单轮）
```mermaid
sequenceDiagram
    participant U as User
    participant R as Runner (ADK)
    participant P as ZhishenMentorPipeline
    participant M as ZhishenMentorAgent
    participant Utl as DialogueUtilityAgent
    Note over U: 可直接输入 .md/.txt 路径进行知识注入

    U->>R: 输入消息/文件
    R->>P: new_message(Content)
    P->>M: before_model_callback (注入 simplified_chat_history)
    M->>Utl: (内部) 意图分析/对话压缩
    Utl-->>M: 压缩结果/标签
    M->>R: 模型回复 (parts/text)
    P-->>R: 状态快照（simplified_chat_history, token_statistics）
```

## 数据与状态
- simplified_chat_history（字符串或结构化摘要）
- token_statistics（单轮/累计 Token 开销）
- 会话存储（按 session_id）：
  - knowledge/ 已注入的文档副本
  - history.jsonl 完整事件流（追加写）
  - compressed_history.json 最新压缩摘要
  - state.json 其他统计

## 关键类位置
- adk_ai_project/main.py：create_main_agent、root_agent、CLI 循环
- adk_ai_project/pipeline.py：Pipeline 与 SnapshotWrapperAgent，实现状态快照
- adk_ai_project/agents/mentor_agent.py：导师 Agent（模型与 persona）
- adk_ai_project/agents/utility_agent.py：工具 Agent（分析/压缩）
- adk_ai_project/callbacks/history_callbacks.py：前后回调（压缩/统计注入）
- adk_ai_project/services/file_session_service.py：文件持久化

## 扩展点
- 模型与端点：通过环境变量 MODEL、OPENAI_API_BASE、OPENAI_API_KEY 调整
- 人格与提示词：集中在 prompts.py 中维护，便于统一风格
- 回调与管道：在 Pipeline 中增加/替换回调，或扩展 SnapshotWrapperAgent 的快照内容
- 记忆/RAG：替换/扩展 FileSessionService，引入检索工具与向量库
- Web API：在 ADK web 基础上封装 REST 层（可选 OpenAPI）

## 限制与注意
- 目前未提供正式 REST 接口文档
- Token 优化效果依赖压缩质量与提示词设计
- 文件注入仅支持 .md/.txt（参见 main.py）

