#!/usr/bin/env python3
"""
测试 FileSessionService 的独立脚本
"""
import os
import sys
import asyncio
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adk_ai_project.services.file_session_service import FileSessionService
from google.adk.events import Event
from google.genai import types as genai_types
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO)

async def test_file_session_service():
    """测试 FileSessionService 的基本功能"""
    
    print("=== 测试 FileSessionService ===")
    
    # 创建测试用的会话服务
    test_base_path = "test_session_data"
    session_service = FileSessionService(base_path=test_base_path)
    
    app_name = "test_app"
    user_id = "test_user"
    session_id = "test_session_001"
    
    try:
        # 1. 测试创建会话
        print("\n1. 测试创建会话...")
        initial_state = {"test_key": "test_value", "counter": 0}
        session = await session_service.create_session(
            app_name=app_name,
            user_id=user_id,
            session_id=session_id,
            state=initial_state
        )
        print(f"✓ 会话创建成功: {session.id}")
        print(f"  - App: {session.app_name}")
        print(f"  - User: {session.user_id}")
        print(f"  - State: {session.state}")
        
        # 2. 测试获取会话
        print("\n2. 测试获取会话...")
        retrieved_session = await session_service.get_session(
            app_name=app_name,
            user_id=user_id,
            session_id=session_id
        )
        if retrieved_session:
            print(f"✓ 会话获取成功: {retrieved_session.id}")
            print(f"  - State: {retrieved_session.state}")
        else:
            print("✗ 会话获取失败")
            return
        
        # 3. 测试添加事件
        print("\n3. 测试添加事件...")
        
        # 创建一个简单的用户消息事件
        user_content = genai_types.Content(
            role="user",
            parts=[genai_types.Part(text="Hello, this is a test message")]
        )
        user_event = Event(
            invocation_id="test_invocation_001",
            author="user",
            content=user_content,
        )
        user_event.id = "event_001"
        user_event.timestamp = time.time()
        user_event.turn_complete = True
        
        await session_service.append_event(retrieved_session, user_event)
        print("✓ 用户事件添加成功")
        
        # 创建一个助手回复事件
        assistant_content = genai_types.Content(
            role="assistant",
            parts=[genai_types.Part(text="Hello! I received your test message.")]
        )
        assistant_event = Event(
            invocation_id="test_invocation_002",
            author="assistant",
            content=assistant_content,
        )
        assistant_event.id = "event_002"
        assistant_event.timestamp = time.time()
        assistant_event.turn_complete = True
        
        await session_service.append_event(retrieved_session, assistant_event)
        print("✓ 助手事件添加成功")
        
        # 4. 测试更新会话状态
        print("\n4. 测试更新会话状态...")
        retrieved_session.state["counter"] = 1
        retrieved_session.state["new_key"] = "new_value"
        await session_service.update_session(retrieved_session)
        print("✓ 会话状态更新成功")
        
        # 5. 重新获取会话验证持久化
        print("\n5. 验证持久化...")
        final_session = await session_service.get_session(
            app_name=app_name,
            user_id=user_id,
            session_id=session_id
        )
        
        if final_session:
            print(f"✓ 持久化验证成功")
            print(f"  - 事件数量: {len(final_session.events)}")
            print(f"  - 最终状态: {final_session.state}")
            
            # 验证事件内容
            if len(final_session.events) >= 2:
                print(f"  - 第一个事件: {final_session.events[0].content.parts[0].text if final_session.events[0].content else 'No content'}")
                print(f"  - 第二个事件: {final_session.events[1].content.parts[0].text if final_session.events[1].content else 'No content'}")
        else:
            print("✗ 持久化验证失败")
        
        # 6. 测试列出会话
        print("\n6. 测试列出会话...")
        sessions = await session_service.list_sessions(app_name, user_id)
        print(f"✓ 找到 {len(sessions)} 个会话")
        for s in sessions:
            print(f"  - {s.id}: {len(s.events)} 个事件")
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print("\n清理测试数据...")
        try:
            await session_service.delete_session(app_name, user_id, session_id)
            print("✓ 测试数据清理完成")
        except Exception as e:
            print(f"清理失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_file_session_service())
