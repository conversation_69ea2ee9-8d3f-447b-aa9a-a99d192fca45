#!/usr/bin/env python3
"""
测试压缩功能的独立脚本
"""
import os
import sys
import asyncio
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adk_ai_project.agents import CompressionSubAgent
from google.adk.sessions import InMemorySessionService
import logging

# 设置日志级别为 DEBUG
logging.basicConfig(level=logging.DEBUG)

async def test_compression_async():
    """测试压缩功能（异步）"""

    # 创建会话
    session_service = InMemorySessionService()
    session = await session_service.create_session(
        app_name="test_app",
        user_id="test_user",
        session_id="test_session",
    )

    # 创建压缩代理
    compression_agent = CompressionSubAgent(
        name="TestCompressionAgent", description="Test compression agent"
    )

    # 测试数据
    user_input = "我想了解运动对大脑的好处"
    assistant_response = """太好了！运动对大脑的好处真的是非常多，而且可能比你想象的还要神奇！

首先，运动能够促进大脑中一种叫做BDNF（脑源性神经营养因子）的物质的产生。你可以把BDNF想象成大脑的"肥料"，它能帮助脑细胞生长，让神经元之间的连接更加牢固。

其次，运动还能刺激海马体的生长。海马体是大脑中负责学习和记忆的重要区域，就像一个图书馆的管理员，负责整理和存储我们的记忆。

最后，运动还能改善大脑的血液循环，为大脑提供更多的氧气和营养，让大脑工作得更高效。

你对这些机制中的哪一个最感兴趣呢？"""

    print("=== 测试压缩功能 ===")
    print(f"用户输入: {user_input}")
    print(f"AI回复: {assistant_response[:100]}...")

    try:
        # 执行压缩
        result = await compression_agent.compress_dialogue_round(
            session=session,
            current_user_input=user_input,
            current_ai_response=assistant_response,
        )

        print("=== 压缩结果 ===")
        print(f"压缩后用户输入: {result['user_input']}")
        print(f"压缩后AI回复: {result['assistant_response']}")

    except Exception as e:
        print(f"压缩失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 在某些环境中需要设置事件循环策略
    if os.name == "nt":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(test_compression_async())
