"""
Compatibility layer for legacy imports. New implementations live under
adk_ai_project/agents/, adk_ai_project/callbacks/, and adk_ai_project/pipeline.py.
This module re-exports callbacks and provides a wrapper CompressionSubAgent
to preserve the public interface used in tests and scripts.
"""

from __future__ import annotations

import logging
import re
from typing import Optional, List, Dict

from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmRequest, LlmResponse
from google.genai import types

from .callbacks.history_callbacks import (
    before_main_model_callback,  # re-export
    after_main_model_callback as _dynamic_after_main_model_callback,
)
from .pipeline import SnapshotWrapperAgent  # re-export
from .agents.utility_agent import DialogueUtilityAgent

logger = logging.getLogger(__name__)

# State keys kept for compatibility
SIMPLIFIED_HISTORY_KEY = "simplified_chat_history"
CURRENT_USER_INPUT_KEY = "current_user_input"
TOKEN_STATS_KEY = "token_statistics"


def after_main_agent_callback(callback_context: CallbackContext) -> Optional[types.Content]:
    logger.info("After agent callback triggered. No action taken.")
    return None


class CompressionSubAgent:
    """
    Backward-compatible wrapper built on DialogueUtilityAgent.
    Exposes compress_dialogue_round and heuristic helpers as before.
    """

    def __init__(self, name: str = "CompressionAgent", description: str = "用于对话历史压缩的Agent"):
        self.name = name
        self.description = description
        self._utility = DialogueUtilityAgent()

    async def compress_dialogue_round(
        self,
        callback_context: CallbackContext,
        current_user_input: str,
        current_ai_response: str,
    ) -> Optional[Dict[str, str]]:
        try:
            simplified_history: List[Dict[str, str]] = callback_context.state.get(
                SIMPLIFIED_HISTORY_KEY, []
            )
            result = await self._utility.run_compression_and_analysis(
                simplified_history=simplified_history,
                current_user_input=current_user_input,
                current_ai_response=current_ai_response,
            )
            if result and isinstance(result.get("compression"), dict):
                comp = result["compression"]
                # 兜底填充
                if not comp.get("user_input") or not comp.get("assistant_response"):
                    heur = self._heuristic_compression(current_user_input, current_ai_response)
                    comp.setdefault("user_input", heur["user_input"]) 
                    comp.setdefault("assistant_response", heur["assistant_response"]) 
                return {"user_input": comp.get("user_input", ""), "assistant_response": comp.get("assistant_response", "")}
            # 回退启发式
            return self._heuristic_compression(current_user_input, current_ai_response)
        except Exception as e:
            logger.error(f"CompressionSubAgent wrapper error: {e}")
            return self._heuristic_compression(current_user_input or "", current_ai_response or "")

    # Heuristic helpers retained for tests/back-compat
    def _compress_user_text(self, text: str) -> str:
        if not text:
            return ""
        s = text.strip()
        s = re.sub(r"\s+", " ", s)
        s = re.sub(r"^(请问|你好|您好|嗨|老师|大佬)[，,。\s]*", "", s)
        s = re.sub(r"(谢谢|非常感谢|多谢)[！!。\s]*$", "", s)
        s = re.sub(r"(我觉得|我想要|我想了解|我想知道|能不能|可不可以|是不是|这个|那个)[，, ]*", "", s)
        clauses = re.split(r"[。！？!?]\s*", s)
        clauses = [c.strip() for c in clauses if c.strip()]
        if clauses:
            s = clauses[0] if len(clauses) == 1 else (clauses[0] + "；" + clauses[1])
        if len(s) > 120:
            s = s[:120] + "..."
        return s.strip()

    def _compress_ai_text(self, text: str) -> str:
        if not text:
            return ""
        s = text.strip()
        s = re.sub(r"\s+", " ", s)
        s = re.sub(r"^(好的|没错|太好了|完全正确|非常好|当然|你好)[，,。\s]*", "", s)
        sentences = re.split(r"(?<=[。！？!?])\s+|(?<=[.;:])\s+", s)
        sentences = [x.strip() for x in sentences if x and len(x.strip()) > 1]
        if not sentences:
            return s
        keywords = [
            "首先",
            "其次",
            "另外",
            "同时",
            "因此",
            "所以",
            "总结",
            "建议",
            "可以",
            "应该",
            "推荐",
            "BDNF",
            "海马体",
            "杏仁核",
            "记忆",
            "专注",
            "压力",
            "运动",
            "效果",
            "机制",
        ]
        picked: List[str] = []
        for sent in sentences:
            if any(k in sent for k in keywords):
                picked.append(sent)
            if len(picked) >= 4:
                break
        if len(picked) < 3:
            for sent in sentences:
                if sent not in picked:
                    picked.append(sent)
                if len(picked) >= 4:
                    break
        compressed = " ".join(picked[:4])
        if len(compressed) > 420:
            compressed = compressed[:420] + "..."
        return compressed.strip()

    def _heuristic_compression(self, user_input: str, ai_response: str) -> Dict[str, str]:
        return {
            "user_input": self._compress_user_text(user_input),
            "assistant_response": self._compress_ai_text(ai_response),
        }

# Re-export the dynamic after-model-callback name for legacy import path
async def after_main_model_callback(
    callback_context: CallbackContext, llm_response: LlmResponse, compression_agent: "CompressionSubAgent" = None
) -> Optional[LlmResponse]:
    # Backward-compat shim: the new after-callback signature requires a DialogueUtilityAgent.
    # If an older caller binds this with a compression_agent, ignore it and use the new path instead.
    # We delegate to the new implementation with a shared utility instance.
    util = DialogueUtilityAgent()
    return await _dynamic_after_main_model_callback(callback_context, llm_response, util)
