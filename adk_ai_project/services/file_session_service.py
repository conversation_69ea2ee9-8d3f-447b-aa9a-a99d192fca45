# adk_ai_project/services/file_session_service.py
import os
import json
import uuid
import time
import shutil
import aiofiles
from typing import Optional
from google.adk.sessions import BaseSessionService, Session
from google.adk.events import Event, EventActions
from google.genai import types as genai_types

class FileSessionService(BaseSessionService):
    """A session service that stores session data in the local filesystem."""

    def __init__(self, base_path: str = "session_data"):
        self._base_path = base_path
        os.makedirs(self._base_path, exist_ok=True)

    async def create_session(self, app_name: str, user_id: str, session_id: str = None, state: dict = None, **kwargs) -> Session:
        """Creates a new session, including its directory structure."""
        # 1. Generate a new session_id if not provided
        if session_id is None:
            session_id = str(uuid.uuid4())

        # 2. Create the directory /session_data/{session_id}
        session_dir = os.path.join(self._base_path, session_id)
        os.makedirs(session_dir, exist_ok=True)

        # 3. Create subdirectories: /knowledge
        knowledge_dir = os.path.join(session_dir, "knowledge")
        os.makedirs(knowledge_dir, exist_ok=True)

        # 4. Create empty files: history.jsonl, compressed_history.json, state.json
        history_file = os.path.join(session_dir, "history.jsonl")
        compressed_history_file = os.path.join(session_dir, "compressed_history.json")
        state_file = os.path.join(session_dir, "state.json")

        # Create empty files if they don't exist
        for file_path in [history_file, compressed_history_file, state_file]:
            if not os.path.exists(file_path):
                async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                    if file_path.endswith('.json'):
                        await f.write('{}')
                    # history.jsonl starts empty (no content needed)

        # 5. Return a new Session object
        initial_state = state or {}
        current_time = time.time()

        session = Session(
            id=session_id,
            app_name=app_name,
            user_id=user_id,
            state=initial_state,
            events=[],
            last_update_time=current_time
        )

        # Save initial state to file
        await self._save_state(session_dir, initial_state)

        return session

    async def get_session(self, app_name: str, user_id: str, session_id: str, **kwargs) -> Session | None:
        """Gets an existing session by loading its data from the filesystem."""
        # 1. Check if the directory /session_data/{session_id} exists
        session_dir = os.path.join(self._base_path, session_id)
        if not os.path.exists(session_dir):
            return None

        try:
            # 2. Read and parse state.json
            state_file = os.path.join(session_dir, "state.json")
            state = {}
            if os.path.exists(state_file):
                async with aiofiles.open(state_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    if content.strip():
                        state = json.loads(content)

            # Read history.jsonl and reconstruct events
            history_file = os.path.join(session_dir, "history.jsonl")
            events = []
            if os.path.exists(history_file):
                async with aiofiles.open(history_file, 'r', encoding='utf-8') as f:
                    async for line in f:
                        line = line.strip()
                        if line:
                            try:
                                event_data = json.loads(line)
                                # Reconstruct Event object from JSON data
                                event = self._json_to_event(event_data)
                                events.append(event)
                            except json.JSONDecodeError:
                                # Skip malformed lines
                                continue

            # Get last update time from the most recent event or file modification time
            last_update_time = 0.0
            if events:
                last_update_time = max(event.timestamp for event in events)
            else:
                # Fallback to file modification time
                if os.path.exists(state_file):
                    last_update_time = os.path.getmtime(state_file)

            # 3. Reconstruct and return the Session object
            session = Session(
                id=session_id,
                app_name=app_name,
                user_id=user_id,
                state=state,
                events=events,
                last_update_time=last_update_time
            )

            return session

        except Exception as e:
            # Log error and return None if session cannot be loaded
            print(f"Error loading session {session_id}: {e}")
            return None

    async def update_session(self, session: Session):
        """Updates the persisted state of a session."""
        # 1. Write the current session.state to state.json
        session_dir = os.path.join(self._base_path, session.id)
        if not os.path.exists(session_dir):
            # Session directory doesn't exist, create it
            await self.create_session(
                app_name=session.app_name,
                user_id=session.user_id,
                session_id=session.id,
                state=session.state
            )
            return

        # Update state file
        await self._save_state(session_dir, session.state)

        # Update last_update_time
        session.last_update_time = time.time()

    async def list_sessions(self, app_name: str, user_id: str) -> list[Session]:
        """Lists all sessions for a given user."""
        sessions = []

        # 1. Scan the /session_data directory for subdirectories
        if not os.path.exists(self._base_path):
            return sessions

        try:
            for item in os.listdir(self._base_path):
                item_path = os.path.join(self._base_path, item)
                if os.path.isdir(item_path):
                    # 2. For each directory, load the session data
                    session = await self.get_session(app_name, user_id, item)
                    if session is not None:
                        # Filter by app_name and user_id
                        if session.app_name == app_name and session.user_id == user_id:
                            sessions.append(session)
        except Exception as e:
            print(f"Error listing sessions: {e}")

        # 3. Return a list of Session objects
        return sessions

    async def delete_session(self, app_name: str, user_id: str, session_id: str):
        """Deletes a session and its data directory."""
        # 1. Remove the entire /session_data/{session_id} directory
        session_dir = os.path.join(self._base_path, session_id)
        if os.path.exists(session_dir):
            try:
                shutil.rmtree(session_dir)
            except Exception as e:
                print(f"Error deleting session {session_id}: {e}")
                raise

    async def append_event(self, session: Session, event: Event):
        """Appends a new event to the session's history file."""
        session_dir = os.path.join(self._base_path, session.id)

        # Ensure session directory exists
        if not os.path.exists(session_dir):
            await self.create_session(
                app_name=session.app_name,
                user_id=session.user_id,
                session_id=session.id,
                state=session.state
            )

        # 1. Convert the Event object to a JSON string
        try:
            event_json = self._event_to_json(event)
            event_line = json.dumps(event_json, ensure_ascii=False)

            # 2. Append the JSON string as a new line to history.jsonl
            history_file = os.path.join(session_dir, "history.jsonl")
            async with aiofiles.open(history_file, 'a', encoding='utf-8') as f:
                await f.write(event_line + '\n')

            # Update session's events list and last_update_time
            session.events.append(event)
            session.last_update_time = time.time()

            # Update session state if event has state_delta
            if hasattr(event, 'state_delta') and event.state_delta:
                for key, value in event.state_delta.items():
                    session.state[key] = value
                # Save updated state
                await self._save_state(session_dir, session.state)

        except Exception as e:
            print(f"Error appending event to session {session.id}: {e}")
            raise

    async def _save_state(self, session_dir: str, state: dict):
        """Save session state to state.json file."""
        state_file = os.path.join(session_dir, "state.json")
        async with aiofiles.open(state_file, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(state, ensure_ascii=False, indent=2))

    def _event_to_json(self, event: Event) -> dict:
        """Convert Event object to JSON-serializable dictionary."""
        try:
            # Use Pydantic's model_dump method if available
            if hasattr(event, 'model_dump'):
                return event.model_dump(exclude_none=True, mode='json')
            else:
                # Fallback: manually extract key attributes
                event_dict = {
                    'id': getattr(event, 'id', None),
                    'timestamp': getattr(event, 'timestamp', time.time()),
                    'invocation_id': getattr(event, 'invocation_id', None),
                    'author': getattr(event, 'author', None),
                    'content': None,
                    'actions': None,
                    'branch': getattr(event, 'branch', None),
                    'partial': getattr(event, 'partial', False),
                    'turn_complete': getattr(event, 'turn_complete', True),
                    'error_code': getattr(event, 'error_code', None),
                    'error_message': getattr(event, 'error_message', None),
                    'interrupted': getattr(event, 'interrupted', False),
                    'grounding_metadata': getattr(event, 'grounding_metadata', None),
                    'custom_metadata': getattr(event, 'custom_metadata', None),
                }

                # Handle content
                if hasattr(event, 'content') and event.content:
                    if hasattr(event.content, 'model_dump'):
                        event_dict['content'] = event.content.model_dump(exclude_none=True, mode='json')
                    else:
                        event_dict['content'] = str(event.content)

                # Handle actions
                if hasattr(event, 'actions') and event.actions:
                    if hasattr(event.actions, 'model_dump'):
                        event_dict['actions'] = event.actions.model_dump(exclude_none=True, mode='json')
                    else:
                        event_dict['actions'] = str(event.actions)

                return event_dict
        except Exception as e:
            print(f"Error converting event to JSON: {e}")
            # Return minimal event representation
            return {
                'id': getattr(event, 'id', str(uuid.uuid4())),
                'timestamp': time.time(),
                'error': f"Serialization failed: {str(e)}"
            }

    def _json_to_event(self, event_data: dict) -> Event:
        """Convert JSON dictionary back to Event object."""
        try:
            # Extract required fields with defaults
            invocation_id = event_data.get('invocation_id', 'unknown')
            author = event_data.get('author', 'system')

            # Create Event with required fields first
            event = Event(
                invocation_id=invocation_id,
                author=author,
            )

            # Set optional fields if they exist
            if 'id' in event_data:
                event.id = event_data['id']
            if 'timestamp' in event_data:
                event.timestamp = event_data['timestamp']
            if 'content' in event_data:
                event.content = self._reconstruct_content(event_data['content'])
            if 'actions' in event_data:
                event.actions = self._reconstruct_actions(event_data['actions'])
            if 'branch' in event_data:
                event.branch = event_data['branch']
            if 'partial' in event_data:
                event.partial = event_data['partial']
            if 'turn_complete' in event_data:
                event.turn_complete = event_data['turn_complete']
            if 'error_code' in event_data:
                event.error_code = event_data['error_code']
            if 'error_message' in event_data:
                event.error_message = event_data['error_message']
            if 'interrupted' in event_data:
                event.interrupted = event_data['interrupted']
            if 'grounding_metadata' in event_data:
                event.grounding_metadata = event_data['grounding_metadata']
            if 'custom_metadata' in event_data:
                event.custom_metadata = event_data['custom_metadata']

            return event

        except Exception as e:
            print(f"Error reconstructing event from JSON: {e}")
            # Return a minimal event with required fields
            return Event(
                invocation_id='error',
                author='system',
                error_message=f"Reconstruction failed: {str(e)}"
            )

    def _reconstruct_content(self, content_data):
        """Reconstruct Content object from JSON data."""
        if not content_data:
            return None

        try:
            # If content_data is already a proper object, return it
            if hasattr(content_data, 'role'):
                return content_data

            # If it's a dict, try to reconstruct Content
            if isinstance(content_data, dict):
                role = content_data.get('role', 'assistant')
                parts_data = content_data.get('parts', [])

                parts = []
                for part_data in parts_data:
                    if isinstance(part_data, dict) and 'text' in part_data:
                        parts.append(genai_types.Part(text=part_data['text']))
                    elif isinstance(part_data, str):
                        parts.append(genai_types.Part(text=part_data))

                return genai_types.Content(role=role, parts=parts)

            # Fallback: treat as text
            return genai_types.Content(
                role='assistant',
                parts=[genai_types.Part(text=str(content_data))]
            )

        except Exception:
            return None

    def _reconstruct_actions(self, actions_data):
        """Reconstruct EventActions object from JSON data."""
        if not actions_data:
            return None

        try:
            # If it's already an EventActions object, return it
            if isinstance(actions_data, EventActions):
                return actions_data

            # If it's a dict, try to reconstruct EventActions
            if isinstance(actions_data, dict):
                return EventActions(
                    skip_summarization=actions_data.get('skip_summarization'),
                    state_delta=actions_data.get('state_delta'),
                    artifact_delta=actions_data.get('artifact_delta'),
                    transfer_to_agent=actions_data.get('transfer_to_agent'),
                    escalate=actions_data.get('escalate'),
                    requested_auth_configs=actions_data.get('requested_auth_configs'),
                )

            # For other types, return None
            return None

        except Exception as e:
            print(f"Error reconstructing actions: {e}")
            return None
