# adk_ai_project/callbacks/history_callbacks.py

import logging
from typing import Optional, List, Dict
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmRequest, LlmResponse
from google.genai import types

from ..agents.utility_agent import DialogueUtilityAgent

logger = logging.getLogger(__name__)

# 会话状态键
SIMPLIFIED_HISTORY_KEY = "simplified_chat_history"
CURRENT_USER_INPUT_KEY = "current_user_input"
TOKEN_STATS_KEY = "token_statistics"

# 待处理知识相关键
PENDING_KNOWLEDGE_KEY = "pending_knowledge"
DYNAMIC_KNOWLEDGE_INSTRUCTION_KEY = "dynamic_knowledge_instruction"


async def after_main_model_callback(
    callback_context: CallbackContext,
    llm_response: LlmResponse,
    utility_agent: DialogueUtilityAgent,
) -> Optional[LlmResponse]:
    """
    在主 Agent 回复后，调用工具 Agent 进行压缩和分析。
    """
    try:
        logger.info(
            f"After model callback (dynamic utils) for agent: {callback_context.agent_name}"
        )

        current_user_input: Optional[str] = callback_context.state.get(
            CURRENT_USER_INPUT_KEY
        )
        ai_original_response: Optional[str] = None
        if llm_response.content and llm_response.content.parts:
            ai_original_response = llm_response.content.parts[0].text or ""

        if not current_user_input or not ai_original_response:
            logger.warning("Missing current input or AI response, skipping utility agent.")
            return None

        # 1. 意图分析
        analysis_result = await utility_agent.classify_intent(current_user_input)
        if analysis_result.get("is_knowledge_injection") is True:
            callback_context.state[PENDING_KNOWLEDGE_KEY] = current_user_input
            logger.info(f"Knowledge injection detected. Reason: {analysis_result.get('reason')}")

        # 2. 对话压缩
        simplified_history: List[Dict[str, str]] = callback_context.state.get(
            SIMPLIFIED_HISTORY_KEY, []
        )
        compression_result = await utility_agent.compress_turn(
            simplified_history=simplified_history,
            current_user_input=current_user_input,
            current_ai_response=ai_original_response,
        )

        # 3. 更新会话状态
        if compression_result:
            simplified_history.append(compression_result)
            # 控制历史长度
            MAX_ROUNDS = 30
            if len(simplified_history) > MAX_ROUNDS:
                del simplified_history[0:len(simplified_history) - MAX_ROUNDS]
            callback_context.state[SIMPLIFIED_HISTORY_KEY] = simplified_history

            # 更新 Token 统计
            def _estimate_tokens(text: str) -> int:
                if not text: return 0
                import re
                chinese_chars = len(re.findall(r"[\u4e00-\u9fff]", text))
                other_chars = len(text) - chinese_chars
                return max(1, int(chinese_chars / 1.5 + other_chars / 4))

            original_tokens = _estimate_tokens(current_user_input) + _estimate_tokens(ai_original_response)
            compressed_tokens = _estimate_tokens(compression_result.get("user_input", "")) + _estimate_tokens(compression_result.get("assistant_response", ""))
            
            token_stats = callback_context.state.get(TOKEN_STATS_KEY, {"total_original_tokens": 0, "total_compressed_tokens": 0, "compression_rounds": 0})
            token_stats["total_original_tokens"] += original_tokens
            token_stats["total_compressed_tokens"] += compressed_tokens
            token_stats["compression_rounds"] += 1
            callback_context.state[TOKEN_STATS_KEY] = token_stats

        return None
    except Exception as e:
        logger.error(f"after_main_model_callback failed: {e}", exc_info=True)
        return None


def before_main_model_callback(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    """
    在主 Agent 调用 LLM 前，动态构建其上下文。
    """
    try:
        logger.info(
            f"Before model callback (dynamic) for agent: {callback_context.agent_name}"
        )

        # 读取当前用户输入并存储到 state 供 after 回调使用
        current_user_input: Optional[str] = None
        if llm_request.contents:
            last_content = llm_request.contents[-1]
            if last_content.role == "user" and last_content.parts:
                current_user_input = last_content.parts[0].text
                callback_context.state[CURRENT_USER_INPUT_KEY] = current_user_input

        # 1) 检查是否有待处理知识，将其注入为动态系统指令
        pending_knowledge = callback_context.state.get(PENDING_KNOWLEDGE_KEY)
        if pending_knowledge:
            dynamic_instruction = (
                "你是‘知深导师’，请吸收以下用户提供的新知识，后续对话中在合适场景中引用与呼应：\n\n"
                + str(pending_knowledge)
            )
            callback_context.state[DYNAMIC_KNOWLEDGE_INSTRUCTION_KEY] = dynamic_instruction
            # 清除待处理知识，避免重复注入
            del callback_context.state[PENDING_KNOWLEDGE_KEY]

        # 2) 如果存在动态指令，则覆盖/设置 system_instruction
        dynamic_instruction = callback_context.state.get(DYNAMIC_KNOWLEDGE_INSTRUCTION_KEY)
        if dynamic_instruction:
            # 确保以 system 指令传给模型
            llm_request.contents = [
                types.Content(role="system", parts=[types.Part(text=dynamic_instruction)])
            ] + [
                msg for msg in (llm_request.contents or []) if msg.role != "system"
            ]

        # 3) 注入简化历史（与旧逻辑一致）
        simplified_history: List[Dict[str, str]] = callback_context.state.get(
            SIMPLIFIED_HISTORY_KEY, []
        )
        messages: List[types.Content] = []
        if simplified_history:
            for pair in simplified_history:
                u = (pair.get("user_input", "") or "").strip()
                a = (pair.get("assistant_response", "") or "").strip()
                if u:
                    messages.append(types.Content(role="user", parts=[types.Part(text=u)]))
                if a:
                    messages.append(
                        types.Content(role="assistant", parts=[types.Part(text=a)])
                    )

        # 重新添加当前用户输入
        if current_user_input:
            messages.append(
                types.Content(role="user", parts=[types.Part(text=current_user_input)])
            )

        if messages:
            # 保留可能已有的 system 指令，替换其余内容
            system_msgs = [m for m in llm_request.contents if m.role == "system"]
            llm_request.contents = system_msgs + messages

        return None
    except Exception as e:
        logger.error(f"before_main_model_callback (dynamic) 失败: {e}")
        return None

