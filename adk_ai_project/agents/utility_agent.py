import os
import re
from typing import Optional, List, Dict, ClassVar
from google.adk.agents import LlmAgent
from google.adk.models import LlmRequest
from google.adk.models.lite_llm import LiteLlm
from google.genai import types
import json
import logging

from ..prompts import UtilityPrompts

logger = logging.getLogger(__name__)

class DialogueUtilityAgent(LlmAgent):
    """
    一个多功能对话工具 Agent，采用“分而治之”策略，独立执行意图分析和对话压缩。
    """

    KNOWLEDGE_INJECTION_THRESHOLD: ClassVar[int] = 500

    ANALYSIS_INSTRUCTION: ClassVar[str] = UtilityPrompts.ANALYSIS_INSTRUCTION

    COMPRESSION_INSTRUCTION: ClassVar[str] = UtilityPrompts.COMPRESSION_INSTRUCTION

    def __init__(self, **kwargs):
        model = kwargs.pop("model", None)
        if model is None:
            model_name_str = os.getenv("MODEL", "gemini-2.5-flash")
            openai_api_key = os.getenv("OPENAI_API_KEY")
            openai_base_url = (
                os.getenv("OPENAI_API_BASE")
                or os.getenv("OPENAI_BASE_URL")
            )
            if openai_base_url and not openai_base_url.startswith("http"):
                openai_base_url = "https://" + openai_base_url
            provider_model = (
                model_name_str if "/" in model_name_str else f"openai/{model_name_str}"
            )
            model = LiteLlm(model=provider_model, api_base=openai_base_url, api_key=openai_api_key)
        
        # Note: The base LlmAgent instruction is not heavily used here as each method supplies its own.
        super().__init__(
            name="DialogueUtilityAgent",
            instruction="A multi-purpose agent for dialogue processing.",
            model=model,
            **kwargs,
        )

    async def classify_intent(self, user_input: str) -> dict:
        """分析用户输入的意图，内置阈值检查。"""
        if len(user_input) < self.KNOWLEDGE_INJECTION_THRESHOLD:
            return {"is_knowledge_injection": False, "reason": "Input below threshold."}

        try:
            llm_request = LlmRequest(
                contents=[
                    types.Content(role="system", parts=[types.Part(text=self.ANALYSIS_INSTRUCTION)]),
                    types.Content(role="user", parts=[types.Part(text=user_input)])
                ]
            )
            response_text = await self._call_llm(llm_request)
            
            is_injection = "knowledge_injection" in (response_text or "").lower()
            return {
                "is_knowledge_injection": is_injection,
                "reason": f"LLM classification result: {response_text}"
            }
        except Exception as e:
            logger.error(f"Intent classification failed: {e}")
            return {"is_knowledge_injection": False, "reason": f"Error during analysis: {e}"}

    async def compress_turn(self, simplified_history: list, current_user_input: str, current_ai_response: str) -> Optional[dict]:
        """压缩单个对话轮次。"""
        try:
            context_parts = []
            recent_history = (simplified_history or [])[-2:]
            if recent_history:
                context_parts.append("## Recent Simplified History")
                for rnd in recent_history:
                    context_parts.append(f"User: {rnd.get('user_input', '')}")
                    context_parts.append(f"AI: {rnd.get('assistant_response', '')}")
            
            context_parts.append("\n## Dialogue to Compress")
            context_parts.append(f"User: {current_user_input}")
            context_parts.append(f"AI: {current_ai_response}")
            
            context_for_llm = "\n".join(context_parts)

            llm_request = LlmRequest(
                contents=[
                    types.Content(role="system", parts=[types.Part(text=self.COMPRESSION_INSTRUCTION)]),
                    types.Content(role="user", parts=[types.Part(text=context_for_llm)])
                ]
            )
            response_text = await self._call_llm(llm_request)
            return self._parse_compression_output(response_text or "")
        except Exception as e:
            logger.error(f"Turn compression failed: {e}")
            return None

    async def _call_llm(self, llm_request: LlmRequest) -> Optional[str]:
        """通用 LLM 调用方法。"""
        try:
            chunks = [part.text async for event in self.model.generate_content_async(llm_request) if event.content for part in event.content.parts if part.text]
            return "".join(chunks).strip()
        except Exception as e:
            logger.warning(f"DialogueUtilityAgent LLM call failed: {e}")
            return None

    def _parse_compression_output(self, text: str) -> Optional[dict]:
        """解析压缩模型的输出。"""
        user_match = re.search(r"User:\s*(.*)", text, re.DOTALL)
        ai_match = re.search(r"AI:\s*(.*)", text, re.DOTALL)
        
        user_text = user_match.group(1).strip() if user_match else ""
        ai_text = ai_match.group(1).strip() if ai_match else ""
        
        if user_text and ai_text:
            return {"user_input": user_text, "assistant_response": ai_text}
        
        logger.warning(f"Could not parse compression output: {text}")
        return None

