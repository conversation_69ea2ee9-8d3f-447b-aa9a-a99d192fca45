import os
from typing import Optional

from google.adk.agents import LlmAgent
from google.adk.models.lite_llm import LiteLlm


class ZhishenMentorAgent(LlmAgent):
    """
    Core mentor agent with a general persona instruction. It does not
    contain any specific domain knowledge; dynamic knowledge is injected
    by callbacks when available.
    """

    def __init__(self, instruction: Optional[str] = None):
        model_name_str = os.getenv("MODEL", "gemini-2.5-flash")
        openai_api_key = os.getenv("OPENAI_API_KEY")
        openai_base_url = (
            os.getenv("OPENAI_API_BASE")
            or os.getenv("OPENAI_BASE_URL")
            or os.getenv("OPENAI_API_BASE")
        )
        if openai_base_url and not openai_base_url.startswith("http"):
            openai_base_url = "https://" + openai_base_url
        provider_model = (
            model_name_str if "/" in model_name_str else f"openai/{model_name_str}"
        )

        base_instruction = (
            instruction
            or "你是‘知深导师’，以清晰、友善、循序渐进的方式进行对话引导。避免加入具体知识，保持通用导师身份。"
        )

        super().__init__(
            name="ZhishenMentorAgent",
            description="Core mentor agent (general persona)",
            instruction=base_instruction,
            model=LiteLlm(model=provider_model, api_base=openai_base_url, api_key=openai_api_key),
        )

