import asyncio
import os
from dotenv import load_dotenv

from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types
from google.adk.models.lite_llm import LiteLlm # 导入 LiteLlm
from adk_ai_project.agents import before_main_model_callback, after_main_model_callback, after_main_agent_callback, SnapshotWrapperAgent

# NOTE: Removed compatibility monkey-patch. Callers should use the ADK async
# session API: `await session_service.get_session(app_name=..., user_id=..., session_id=...)`.

# NOTE: The compatibility monkey-patch above is temporary. For a cleaner design,
# tests should call the async API `await session_service.get_session(app_name=..., user_id=..., session_id=...)`.

# try:
#     import openai  # 不再直接导入 openai，转而使用 LiteLlm
# except Exception:  # pragma: no cover
#     openai = None

# 加载环境变量
load_dotenv()

# 从环境中获取模型名称和 OpenAI 配置
MODEL_NAME = os.getenv("MODEL", "gemini-2.5-flash") # 更新默认模型名称以保持一致性
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_BASE_URL = os.getenv("OPENAI_API_BASE") or os.getenv("OPENAI_BASE_URL") or os.getenv("OPENAI_API_BASE")
if OPENAI_BASE_URL and not OPENAI_BASE_URL.startswith("http"):
    OPENAI_BASE_URL = "https://" + OPENAI_BASE_URL

def load_file_content(filepath: str) -> str:
    """加载文件内容"""
    try:
        # 确保路径相对于当前文件是正确的
        # __file__ 是当前脚本的路径
        base_dir = os.path.dirname(os.path.abspath(__file__))
        # 我们需要从 adk_ai_project 目录往上一层找到 real_dialogues
        # 所以路径应该是 ../real_dialogues/
        full_path = os.path.join(base_dir, '..', filepath)
        with open(full_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"错误: 文件未找到 - {filepath}")
        return ""

def create_main_agent() -> LlmAgent:
    """
    创建并配置主 LlmAgent。
    """
    print("Loading persona and knowledge materials...")
    persona_prompt = load_file_content("real_dialogues/persona-prompt.md")
    knowledge_material = load_file_content("real_dialogues/大脑健身房.md")

    if not persona_prompt or not knowledge_material:
        raise ValueError("未能加载 persona-prompt 或 knowledge_material，请检查文件路径。")

    # 将知识材料整合到 instruction 中
    main_agent_instruction = f"""
{persona_prompt}

---
**参考知识材料：《大脑健身房》**
{knowledge_material}
---

请严格遵循你的“知深导师”身份和指导哲学，并利用上述知识材料与用户进行深度对话。
"""

    print("Creating main LlmAgent...")
    # Ensure LiteLlm treats the model as OpenAI-compatible endpoint
    provider_model = MODEL_NAME if "/" in MODEL_NAME else f"openai/{MODEL_NAME}"

    base_llm_agent = LlmAgent(
        name="ZhishenMentorAgent",
        instruction=main_agent_instruction,
        description="一个扮演知深导师角色的AI，通过对话历史压缩优化Token消耗。",
        # 注册回调函数
        before_model_callback=before_main_model_callback,
        after_model_callback=after_main_model_callback,
        after_agent_callback=after_main_agent_callback,
        # 明确配置为使用 LiteLlm 适配层
        model=LiteLlm(
            model=provider_model, # 将模型名称（带提供商前缀）传递给 LiteLlm
            api_base=OPENAI_BASE_URL,
            api_key=OPENAI_API_KEY,
        ),
        sub_agents=[],
        tools=[]
    )
    # 用 SnapshotWrapperAgent 包装，确保事件流包含 state_delta 快照
    wrapped = SnapshotWrapperAgent(
        name="ZhishenMentorPipeline",
        description="包含主对话与快照事件的管道代理",
        inner_agent=base_llm_agent,
    )
    return wrapped

async def main():
    """
    主函数，启动 Agent 并进行交互式对话。
    """
    main_agent = create_main_agent()

    # 设置 Runner 和 Session
    app_name = "adk_zhishen_mentor"
    user_id = "test_user_01"
    session_id = "test_session_01"
    
    session_service = InMemorySessionService()
    runner = Runner(
        agent=main_agent,
        app_name=app_name,
        session_service=session_service
    )
    
    # 创建一个新的会话
    session = await session_service.create_session(
        app_name=app_name, user_id=user_id, session_id=session_id
    )
    print(f"Session '{session_id}' created.")
    print("-" * 30)
    print("知深导师已准备就绪，请输入您的问题（输入 'exit' 退出）：")
    print("-" * 30)

    while True:
        try:
            user_input = await asyncio.to_thread(input, "🧑‍💻 User: ")
            if user_input.lower() == 'exit':
                print("对话结束。")
                break

            user_content = types.Content(role='user', parts=[types.Part(text=user_input)])
            
            print("🤖 Assistant: ", end="", flush=True)
            final_response_text = ""
            async for event in runner.run_async(
                user_id=user_id, session_id=session_id, new_message=user_content
            ):
                if event.is_model_response() and event.content and event.content.parts:
                    # 流式输出
                    print(event.content.parts[0].text, end="", flush=True)
                if event.is_final_response() and event.content and event.content.parts:
                    final_response_text = event.content.parts[0].text.strip()
            
            print("\n") # 换行

            # 打印当前会话状态中的简化历史，用于调试 (使用 async API)
            current_session = await session_service.get_session(app_name=app_name, user_id=user_id, session_id=session_id)
            simplified_history = current_session.state.get("simplified_chat_history", [])
            print("-" * 20)
            print(f"DEBUG: Simplified history has {len(simplified_history)} rounds.")
            if simplified_history:
                print("Last simplified round:")
                print(simplified_history[-1])
            print("-" * 20)


        except (KeyboardInterrupt, EOFError):
            print("\n对话结束。")
            break
        except Exception as e:
            print(f"\n发生错误: {e}")
            break

if __name__ == "__main__":
    # 在 Python 3.8+ 的 Windows 系统上，需要设置事件循环策略
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    try:
        asyncio.run(main())
    except RuntimeError as e:
        if "cannot be called from a running event loop" in str(e):
            print("错误：不能在已运行的事件循环中调用 asyncio.run()。请在普通 Python 环境中运行此脚本。")
        else:
            raise e
