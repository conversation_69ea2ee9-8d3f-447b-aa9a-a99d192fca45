# 产品需求文档 (PRD)

## 1. 引言

### 1.1 项目背景

本项目旨在利用 `google/adk-python` 框架的强大功能，构建一个创新的对话 AI 产品——“知深导师 AI”。该产品聚焦于在提供卓越用户体验的同时，通过后端技术优化显著降低运营成本。我们前期已经完成了一次非常成功的实验，验证了对话历史Token优化方案的可行性，现在需要将此方案产品化，并结合ADK的更多特性，打造一个前后台高度分离且性能优异的智能导师。

### 1.2 项目目标

*   **用户价值**: 提供一个能够深度互动、个性化指导的学习伙伴，帮助用户从“熟知”到“真知”。
*   **业务价值**: 通过 Token 优化，大幅降低 LLM 调用成本，提升产品市场竞争力。
*   **技术价值**: 验证 `adk-python` 在构建复杂、高性能对话 Agent 方面的能力，并形成可复用的技术资产。

### 1.3 术语定义

*   **ADK**: Agent Development Kit, Google 开源的 Agent 开发框架。
*   **LLM**: Large Language Model, 大型语言模型。
*   **知深导师 AI**: 本项目开发的目标对话 AI 产品。
*   **CompressionSubAgent**: 负责对话历史增量压缩的子 Agent。
*   **simplified_chat_history**: 经过压缩和精简的对话历史记录。
*   **Token 优化**: 通过减少 LLM 输入的 Token 数量，降低 API 成本和提高响应速度。
*   **RAG**: Retrieval Augmented Generation, 检索增强生成，一种结合信息检索和文本生成的 AI 技术。

## 2. 产品概述

知深导师 AI 将是一个基于对话的智能学习系统，旨在模拟一位经验丰富的导师，通过互动式的教学方式，引导用户深入理解复杂概念。用户在前台将体验到流畅、连贯、无延迟的对话，而所有复杂的 Token 优化和上下文管理逻辑将在后台透明地运行。

## 3. 功能需求

### 3.1 核心功能

*   **智能多轮对话**: 能够理解用户意图，维护多轮对话上下文，并提供相关、有深度、遵循费曼学习法的回复。
*   **知深导师角色扮演**: 严格遵循集中化的 `adk_ai_project/prompts.py` 中 `MentorPrompts.GENERAL_PERSONA` 定义的导师身份、哲学与教学框架。
*   **对话历史智能压缩 (后台)**:
    *   `CompressionSubAgent` 自动对每一轮用户和 AI 的原始发言进行增量压缩和信息提炼。
    *   `simplified_chat_history` 实时更新，作为后续 LLM 请求的核心上下文。
    *   前台用户对此压缩过程无感知。
*   **Token 消耗优化 (后台)**:
    *   在每次 LLM 请求发送前，自动将 `simplified_chat_history` 注入 `LlmRequest`，取代原始的完整对话历史。
    *   显著降低每次 LLM 调用的 Token 数量，从而减少成本。
*   **知识材料集成**:
    *   初期通过主 Agent 的 `instruction` 或额外上下文提供知识材料（例如《大脑健身房》核心观点）。
    *   未来将集成 `MemoryService` 或 `KnowledgeBaseTool`，实现动态的知识检索和注入（RAG）。

### 3.2 非功能性需求

*   **性能**:
    *   **响应速度**: 对话响应时间（从用户输入到 AI 回复显示）应在 3 秒以内（目标）。
    *   **并发能力**: 系统应能支持至少 X 名并发用户（具体数字待定），同时保持稳定的性能。
    *   **Token 成本**: 相较于非优化方案，Token 消耗应至少降低 Y%（具体数字待定）。
*   **可靠性**:
    *   **可用性**: 系统应确保 99.9% 的正常运行时间。
    *   **错误处理**: 对 LLM API 调用失败、网络中断、Agent 逻辑异常等情况有完善的错误处理和重试机制。
*   **可扩展性**:
    *   Agent 结构应模块化，易于扩展新的功能 Agent 或优化策略。
    *   记忆服务应支持不同类型的知识库集成。
*   **安全性**:
    *   用户对话数据传输和存储应加密。
    *   防止恶意提示词注入。
*   **可维护性**:
    *   代码结构清晰，文档完善。
    *   易于调试和监控。

## 4. 技术方案概述 (基于 `adk-python`)

### 4.2 关键技术实现

*   **对话压缩**: 由 `DialogueUtilityAgent`（基于 `LlmAgent`）提供，封装在兼容包装器 `CompressionSubAgent` 中，通过回调管理 `simplified_chat_history`。
*   **回调集成**:
    *   `after_model_callback`: 在此调用 `CompressionSubAgent` 对 `LlmResponse` 和用户输入进行压缩，更新 `ctx.session.state["simplified_chat_history"]`。
    *   `before_model_callback`: 在此从 `ctx.session.state` 读取 `simplified_chat_history`，修改 `LlmRequest` 将其注入为上下文。
*   **Prompt Engineering**: 精心设计主 Agent 和 `CompressionSubAgent` 的 Prompt，以确保压缩的有效性和导师风格的一致性。
*   **状态管理**: 利用 `ctx.session.state` 进行跨 Agent 和跨轮次的 `simplified_chat_history` 管理。

## 5. 验收标准

*   **功能验收**:
    *   多轮对话功能正常，符合导师角色设定。
    *   后台 `simplified_chat_history` 正常生成和更新。
    *   LLM 请求中包含的是 `simplified_chat_history` 而非完整历史。
    *   知识材料能够被 Agent 有效利用。
*   **性能验收**:
    *   平均响应时间 ≤ 3 秒。
    *   Token 消耗量相较于基线方案降低 Y%。
*   **用户体验验收**:
    *   用户反馈无延迟、无功能缺失。
    *   导师 AI 对话的连贯性、准确性、教学深度和风格得到用户高度评价。

## 6. 附录

*   **`adk_ai_project/prompts.py`**: 知深导师 AI 身份设定的集中来源。
*   **`adk_ai_technical_details.md`**: 技术与实施细节文档（已与当前实现对齐）。
