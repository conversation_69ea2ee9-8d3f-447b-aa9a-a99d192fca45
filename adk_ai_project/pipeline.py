import logging
from typing import Optional

from google.adk.agents import LlmAgent, BaseAgent
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event, EventActions

# Shared state keys
SIMPLIFIED_HISTORY_KEY = "simplified_chat_history"
TOKEN_STATS_KEY = "token_statistics"

logger = logging.getLogger(__name__)


class SnapshotWrapperAgent(BaseAgent):
    """
    Lightweight wrapper that runs an inner LlmAgent, then yields a final
    state snapshot event containing simplified history and token stats.
    """

    def __init__(self, name: str, description: str, inner_agent: LlmAgent):
        super().__init__(name=name, description=description, sub_agents=[inner_agent])

    async def _run_async_impl(self, ctx: InvocationContext):
        inner = self.sub_agents[0] if self.sub_agents else None
        if inner is not None:
            async for event in inner.run_async(ctx):
                yield event

        try:
            state_delta = {
                SIMPLIFIED_HISTORY_KEY: ctx.session.state.get(SIMPLIFIED_HISTORY_KEY, []),
                TOKEN_STATS_KEY: ctx.session.state.get(TOKEN_STATS_KEY, {}),
            }
            actions = EventActions(state_delta=state_delta)
            yield Event(
                invocation_id=getattr(ctx, "invocation_id", ""),
                author=self.name or "system",
                actions=actions,
            )
        except Exception:
            return


class ZhishenMentorPipeline(SnapshotWrapperAgent):
    """
    Top-level pipeline Agent that coordinates the mentor agent and yields a
    state snapshot for downstream consumers.
    """

    def __init__(self, mentor_agent: LlmAgent):
        super().__init__(
            name="ZhishenMentorPipeline",
            description="Top-level pipeline for Zhishen Mentor",
            inner_agent=mentor_agent,
        )

