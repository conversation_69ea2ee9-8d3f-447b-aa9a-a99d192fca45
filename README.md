# ADK 知深导师 AI

> 一个基于 google/adk-python 的对话式“深度导师”AI，聚焦于多轮对话的 Token 优化与高质量教学体验。

## 特性
- 多轮对话：在保留语义连续性的同时显著降低 Token 消耗
- 角色设定：集中式导师人格（MentorPrompts.GENERAL_PERSONA）
- 对话压缩：辅助手 Agent 进行意图分析与历史压缩（DialogueUtilityAgent）
- 管道编排：顶层 Pipeline 统一治理对话流、生成快照（ZhishenMentorPipeline/SnapshotWrapperAgent）
- 会话持久化：FileSessionService 将历史与知识材料落盘
- 运行方式：CLI 交互与 ADK Web Demo

## 快速开始

### 1) 环境准备
- Python 3.11+
- 建议使用虚拟环境
- 配置 .env：
  - OPENAI_API_KEY=...（或其他 LiteLLM 兼容源）
  - OPENAI_API_BASE=...（可选）
  - MODEL=gemini-2.5-flash（默认，可改为 openai/gpt-4o-mini 等）

### 2) 运行 CLI
```bash
python -m adk_ai_project.main
```
提示符出现后可直接输入问题；也可直接输入本地 .md/.txt 文件路径进行“知识注入”。

### 3) 运行 Web Demo
```bash
bash start_web_demo.sh
# 浏览器打开 http://127.0.0.1:8000
```

## 目录结构
```
.
├── adk_ai_project/
│   ├── agents/                # 核心 Agent（导师/工具）
│   ├── callbacks/             # 前后回调（token 优化关键）
│   ├── services/              # FileSessionService 等
│   ├── main.py                # CLI 入口 & root_agent 构建
│   ├── pipeline.py            # Pipeline 与 SnapshotWrapperAgent
│   ├── prompts.py             # 集中化导师/工具提示词
│   ├── brief.md | prd.md      # 项目简介/PRD（中文）
│   └── agent.py               # 向后兼容的入口导出
├── docs/
│   ├── architecture.md        # 系统架构（含 Mermaid）
│   ├── dev_guide.md           # 开发者指南
│   ├── user_guide.md          # 用户指南
│   └── api_reference.md       # API 参考
├── start_web_demo.sh          # 启动 ADK Web Demo
├── test_file_session_service.py
└── GEMINI.md / docs/*         # 设计说明与实现计划（中文）
```

## 架构总览
- 顶层 Pipeline：ZhishenMentorPipeline 负责组织调用、统一注入/产出状态
- 主导师：ZhishenMentorAgent 按 GENERAL_PERSONA 进行对话
- 工具 Agent：DialogueUtilityAgent 拆分“意图分析 + 对话压缩”两阶段
- 回调：before_main_model_callback/after_main_model_callback 分别在模型前后执行压缩与统计
- 状态键：
  - simplified_chat_history
  - token_statistics
- 会话持久化：FileSessionService 将 history.jsonl、compressed_history.json、knowledge/ 落盘

详见 docs/architecture.md。

## 配置与环境变量
- OPENAI_API_KEY：OpenAI 或兼容端点的 Key
- OPENAI_API_BASE：可选，兼容的 Base URL（自动补 https://）
- MODEL：默认 gemini-2.5-flash；也可设置为 openai/gpt-4o-mini 等（自动补 openai/ 前缀）

## 故障排查
- 无法连通模型：检查 OPENAI_API_KEY/OPENAI_API_BASE
- Token 仍然偏高：确认回调是否生效，查看 session_data/*/compressed_history.json
- Web Demo 无法访问：确保 adk 已安装并在 PATH 中；端口被占用时修改 start_web_demo.sh 的 --port

## 常见问题
- 是否支持知识注入？
  - CLI 下直接输入 .md/.txt 路径，系统会以“请学习以下文档内容：...”形式注入
- 是否有 REST API？
  - 目前使用 ADK 提供的 web 容器；如需公开 REST/OpenAPI，请在此基础上封装服务层

## 相关文档
- adk_ai_project/brief.md
- adk_ai_project/prd.md
- docs/implementation_plan.md
- docs/adk_agent_token_optimization_design_doc.md
- GEMINI.md

