# GEMINI.md

## Project Overview

This project, "ADK Deep Mentor AI" (知深导师 AI), is a conversational AI built using the `adk-python` framework. Its primary goal is to create a "deep mentor" AI that can engage in long, coherent, and knowledge-rich conversations while minimizing token consumption and operational costs.

The core of the project is a token optimization strategy that uses a `DialogueUtilityAgent` to compress the conversation history in real-time. This compressed history is then used as context for the main `ZhishenMentorAgent`, significantly reducing the number of tokens sent to the LLM in each turn.

The AI's persona is meticulously defined in `adk_ai_project/prompts.py`. It's designed to be a "closed-loop learning partner" that guides users from superficial knowledge to deep understanding, using the <PERSON><PERSON><PERSON> learning method as its core philosophy.

### Key Technologies

*   **Core Framework:** `adk-python`
*   **Language Model:** Gemini (or other LiteLlm-compatible models)
*   **Programming Language:** Python

### Architecture

The project follows a modular architecture with a clear separation of concerns:

*   **`ZhishenMentorPipeline`:** The top-level agent that orchestrates the entire conversation flow.
*   **`ZhishenMentorAgent`:** The core agent that embodies the "Zhishen Mentor" persona.
*   **`DialogueUtilityAgent`:** A helper agent responsible for intent classification and conversation compression.
*   **Callbacks (`history_callbacks.py`):** These functions implement the token optimization logic by intercepting the conversation flow before and after the main agent is called.
*   **Prompts (`prompts.py`):** A centralized file that defines the personas and instructions for all agents.

## Building and Running

To run the project, execute the following command from the root directory:

```bash
python -m adk_ai_project.main
```

This will start the interactive command-line interface for the "Zhishen Mentor AI".

### Testing

The project includes a `test_adk_system.py` file, which can be used to run system-level tests. To run the tests, execute the following command:

```bash
python test_adk_system.py
```

## Development Conventions

*   **Configuration:** The project uses a `.env` file for configuration. The `MODEL`, `OPENAI_API_KEY`, and `OPENAI_API_BASE` environment variables are used to configure the LLM.
*   **Prompts:** All prompts are centralized in `adk_ai_project/prompts.py` for easy maintenance and versioning.
*   **State Management:** The session state (`ctx.session.state`) is used to store the `simplified_chat_history` and other relevant information across conversation turns.
*   **Callbacks:** The `before_model_callback` and `after_model_callback` hooks are used to implement the token optimization logic in a non-intrusive way.
