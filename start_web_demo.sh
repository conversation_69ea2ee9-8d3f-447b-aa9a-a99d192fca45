#!/bin/bash
# 这是一个用于启动“知深导师”AI Web Demo 的便捷脚本。
# 它会启动 ADK 的后端服务以及内置的前端用户界面。

# 在运行此脚本前，请确保：
# 1. 您已经使用 'source .venv/bin/activate' (或类似命令) 激活了您的 Python 虚拟环境。
# 2. 您的 .env 文件已配置好，或已在环境中设置了必要的 API 密钥。

echo "正在启动“知深导师” AI Web Demo..."
echo "服务启动后，请在浏览器中打开 http://127.0.0.1:8000"
echo "--------------------------------------------------"

# 运行 ADK Web 服务器，加载 adk_ai_project 中的 agent，并在 8000 端口上提供服务
adk web adk_ai_project/ --port 8000
